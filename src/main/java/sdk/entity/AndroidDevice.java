package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.interfaces.IAndroid;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Android设备
 */
@Slf4j
public class AndroidDevice extends DefaultVisionDevice implements IAndroid {
    private volatile InputStream currentVideoStream;
    private volatile boolean isStreamingVideo = false;

    public AndroidDevice() {
        super(DeviceModel.Android.USB_ANDROID);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ANDROID.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ANDROID;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<AndroidDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        JsonResponse<List<AndroidDevice>> resp = defaultGetJsonResponse(UrlConstants.DeviceUrls.PhysicalPortUrls.getAllAndroids(getDeviceModel()),
                new TypeReference<JsonResponse<List<AndroidDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult screenshot() {
        return callOperationMethod(DeviceMethods.screenshot);
    }

    @Override
    public InputStream videoStream() {
        return videoStream(0, 0, 0);
    }

    @Override
    public InputStream videoStream(int width, int height, int bitRate) {
        try {
            String deviceName = getDeviceName();
            if (deviceName == null || deviceName.trim().isEmpty()) {
                log.error("设备名称为空，无法启动视频流");
                return null;
            }

            // 构建请求URL
            String url;
            if (width == 0 && height == 0 && bitRate == 0) {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrl(deviceName);
            } else {
                url = UrlConstants.AndroidScreencapUrls.getVideoStreamUrlWithParams(deviceName, width, height, bitRate);
            }

            log.info("请求Android设备视频流: 设备={}, URL={}", deviceName, url);

            // 启动视频流
            InputStream inputStream = getVideoStreamInputStream(url);
            if (inputStream != null) {
                log.info("Android设备视频流启动成功: 设备={}, 分辨率={}x{}, 比特率={}",
                        deviceName, width, height, bitRate);
                currentVideoStream = inputStream;
                isStreamingVideo = true;
            } else {
                log.error("Android设备视频流启动失败: 设备={}", deviceName);
            }
            return inputStream;

        } catch (Exception e) {
            log.error("Android设备视频流启动异常: 设备={}, 错误={}", getDeviceName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 停止视频流
     *
     * @return 是否成功停止视频流
     */
    public boolean stopVideoStream() {
        if (!isStreamingVideo || currentVideoStream == null) {
            return true;
        }

        try {
            // 关闭当前视频流
            currentVideoStream.close();

            // 发送停止请求到服务器
            String deviceName = getDeviceName();
            String url = UrlConstants.AndroidScreencapUrls.getStopVideoStreamUrl(deviceName);

            Request request = new Request.Builder()
                    .url(url)
                    .delete()
                    .headers(BaseHttpClient.formHeaders())
                    .build();

            try (Response response = new OkHttpClient().newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.info("成功停止设备{}的视频流", deviceName);
                } else {
                    log.warn("停止设备{}视频流时收到非成功响应: {}", deviceName, response.code());
                }
            }

            return true;
        } catch (Exception e) {
            log.error("停止视频流时发生错误: {}", e.getMessage(), e);
            return false;
        } finally {
            isStreamingVideo = false;
            currentVideoStream = null;
        }
    }

    /**
     * 获取视频流的InputStream
     *
     * @param url 视频流URL
     * @return InputStream，失败时返回null
     */
    private InputStream getVideoStreamInputStream(String url) {
        try {
            // 创建专用于2K视频流的HTTP客户端（高性能配置）
            OkHttpClient streamHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)  // 增加连接超时，应对2K数据量
                    .readTimeout(0, TimeUnit.SECONDS)      // 无读取超时
                    .writeTimeout(0, TimeUnit.SECONDS)     // 无写入超时
                    .retryOnConnectionFailure(false)       // 禁用重试，避免延迟
                    .protocols(Collections.singletonList(Protocol.HTTP_1_1)) // 强制HTTP/1.1，更稳定
                    .build();

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .headers(BaseHttpClient.formHeaders())
                    .build();

            log.info("发送视频流请求: {}", url);

            // 发送请求
            Response response = streamHttpClient.newCall(request).execute();

            if (!response.isSuccessful()) {
                log.error("请求视频流失败: 状态码={}, 消息={}", response.code(), response.message());
                response.close();
                return null;
            }

            if (response.body() == null) {
                log.error("视频流响应体为空");
                response.close();
                return null;
            }

            // 记录响应头信息，帮助调试
            log.info("视频流响应头信息:");
            log.info("  Content-Type: {}", response.header("Content-Type"));
            log.info("  X-Device-Name: {}", response.header("X-Device-Name"));
            log.info("  X-Video-Resolution: {}", response.header("X-Video-Resolution"));
            log.info("  X-Video-BitRate: {}", response.header("X-Video-BitRate"));
            log.info("  Connection: {}", response.header("Connection"));
            log.info("  Cache-Control: {}", response.header("Cache-Control"));

            log.info("视频流请求成功，开始接收数据流");

            // 返回响应体的InputStream
            return response.body().byteStream();

        } catch (IOException e) {
            log.error("获取视频流InputStream失败: {}", e.getMessage(), e);
            return null;
        }
    }

    public OperationResult executeAdbCommand(String command) {
        return callOperationMethod(DeviceMethods.executeADBCommand, command);
    }
}
