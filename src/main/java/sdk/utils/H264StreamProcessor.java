package sdk.utils;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.*;
import org.bytedeco.javacv.Frame;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * H.264数据流处理器
 * 使用JavaCV和FFmpeg处理H.264视频流
 */
@Slf4j
public class H264StreamProcessor {
    
    private FFmpegFrameGrabber grabber;
    private Java2DFrameConverter converter;
    private final BlockingQueue<BufferedImage> frameQueue;
    private final AtomicBoolean isRunning;
    private final AtomicBoolean isProcessing;
    private Thread processingThread;
    
    // 配置参数
    private static final int FRAME_QUEUE_SIZE = 10;
    private static final int TIMEOUT_SECONDS = 30;
    
    public H264StreamProcessor() {
        this.frameQueue = new LinkedBlockingQueue<>(FRAME_QUEUE_SIZE);
        this.isRunning = new AtomicBoolean(false);
        this.isProcessing = new AtomicBoolean(false);
        this.converter = new Java2DFrameConverter();
    }
    
    /**
     * 从输入流处理H.264数据
     * @param inputStream H.264数据流
     * @return 是否启动成功
     */
    public boolean processH264Stream(InputStream inputStream) {
        if (isRunning.get()) {
            log.warn("H.264处理器已经在运行");
            return false;
        }
        
        try {
            // 创建FFmpeg帧抓取器
            grabber = new FFmpegFrameGrabber(inputStream);
            
            // 配置H.264解码器
            configureH264Decoder();
            
            // 启动抓取器
            grabber.start();
            
            isRunning.set(true);
            isProcessing.set(true);
            
            // 启动处理线程
            startProcessingThread();
            
            log.info("H.264流处理器启动成功");
            return true;
            
        } catch (Exception e) {
            log.error("启动H.264流处理器失败: {}", e.getMessage(), e);
            cleanup();
            return false;
        }
    }
    
    /**
     * 从文件处理H.264数据
     * @param h264FilePath H.264文件路径
     * @return 是否启动成功
     */
    public boolean processH264File(String h264FilePath) {
        if (isRunning.get()) {
            log.warn("H.264处理器已经在运行");
            return false;
        }
        
        try {
            // 创建FFmpeg帧抓取器
            grabber = new FFmpegFrameGrabber(h264FilePath);
            
            // 配置H.264解码器
            configureH264Decoder();
            
            // 启动抓取器
            grabber.start();
            
            isRunning.set(true);
            isProcessing.set(true);
            
            // 启动处理线程
            startProcessingThread();
            
            log.info("H.264文件处理器启动成功: {}", h264FilePath);
            return true;
            
        } catch (Exception e) {
            log.error("启动H.264文件处理器失败: {}", e.getMessage(), e);
            cleanup();
            return false;
        }
    }
    
    /**
     * 配置H.264解码器
     */
    private void configureH264Decoder() {
        try {
            // 设置视频格式为H.264
            grabber.setFormat("h264");
            
            // 设置视频编解码器
            grabber.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            
            // 设置像素格式
            grabber.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
            
            // 设置超时时间
            grabber.setOption("timeout", String.valueOf(TIMEOUT_SECONDS * 1000000)); // 微秒
            
            // 设置分析时长（微秒）
            grabber.setOption("analyzeduration", "5000000"); // 5秒
            
            // 设置探测大小
            grabber.setOption("probesize", "1048576"); // 1MB
            
            // 启用快速解码
            grabber.setOption("flags", "low_delay");
            
            // 设置线程数
            grabber.setOption("threads", "auto");
            
            log.info("H.264解码器配置完成");
            
        } catch (Exception e) {
            log.error("配置H.264解码器失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 启动处理线程
     */
    private void startProcessingThread() {
        processingThread = new Thread(this::processFrames, "H264-Processor");
        processingThread.setDaemon(true);
        processingThread.start();
    }
    
    /**
     * 处理视频帧
     */
    private void processFrames() {
        log.info("开始处理H.264视频帧");
        
        long frameCount = 0;
        long lastLogTime = System.currentTimeMillis();
        
        try {
            while (isProcessing.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 抓取帧
                    Frame frame = grabber.grab();
                    
                    if (frame == null) {
                        log.info("H.264流结束，共处理 {} 帧", frameCount);
                        break;
                    }
                    
                    // 只处理图像帧
                    if (frame.image != null) {
                        // 转换为BufferedImage
                        BufferedImage bufferedImage = converter.convert(frame);
                        
                        if (bufferedImage != null) {
                            // 如果队列满了，移除最老的帧
                            if (frameQueue.remainingCapacity() == 0) {
                                frameQueue.poll();
                            }
                            
                            // 添加新帧到队列
                            frameQueue.offer(bufferedImage);
                            frameCount++;
                            
                            // 定期输出统计信息
                            long currentTime = System.currentTimeMillis();
                            if (currentTime - lastLogTime > 5000) { // 每5秒输出一次
                                log.info("已处理 {} 帧，队列大小: {}", frameCount, frameQueue.size());
                                lastLogTime = currentTime;
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    if (isProcessing.get()) {
                        log.error("处理H.264帧时出错: {}", e.getMessage());
                        Thread.sleep(100); // 错误后短暂休眠
                    }
                }
            }
            
        } catch (InterruptedException e) {
            log.info("H.264处理线程被中断");
        } catch (Exception e) {
            log.error("H.264处理线程异常: {}", e.getMessage(), e);
        } finally {
            log.info("H.264帧处理结束，总共处理 {} 帧", frameCount);
        }
    }
    
    /**
     * 获取下一帧图像
     * @return BufferedImage或null
     */
    public BufferedImage getNextFrame() {
        return frameQueue.poll();
    }
    
    /**
     * 获取下一帧图像（阻塞等待）
     * @param timeoutMs 超时时间（毫秒）
     * @return BufferedImage或null
     */
    public BufferedImage getNextFrame(long timeoutMs) {
        try {
            return frameQueue.poll(timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }
    
    /**
     * 获取队列中的帧数量
     * @return 帧数量
     */
    public int getQueueSize() {
        return frameQueue.size();
    }
    
    /**
     * 是否正在运行
     * @return 运行状态
     */
    public boolean isRunning() {
        return isRunning.get();
    }
    
    /**
     * 停止处理
     */
    public void stop() {
        log.info("正在停止H.264流处理器...");
        
        isProcessing.set(false);
        isRunning.set(false);
        
        // 中断处理线程
        if (processingThread != null && processingThread.isAlive()) {
            processingThread.interrupt();
            try {
                processingThread.join(3000); // 等待3秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        cleanup();
        log.info("H.264流处理器已停止");
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (grabber != null) {
                grabber.stop();
                grabber.release();
                grabber = null;
            }
            
            if (converter != null) {
                converter.close();
                converter = null;
            }
            
            frameQueue.clear();
            
        } catch (Exception e) {
            log.error("清理H.264处理器资源时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 保存帧为图片文件
     * @param frame 图像帧
     * @param outputPath 输出路径
     * @param format 图片格式（jpg, png等）
     * @return 是否保存成功
     */
    public static boolean saveFrame(BufferedImage frame, String outputPath, String format) {
        try {
            File outputFile = new File(outputPath);
            outputFile.getParentFile().mkdirs();
            return ImageIO.write(frame, format, outputFile);
        } catch (Exception e) {
            log.error("保存帧图片失败: {}", e.getMessage());
            return false;
        }
    }
}
