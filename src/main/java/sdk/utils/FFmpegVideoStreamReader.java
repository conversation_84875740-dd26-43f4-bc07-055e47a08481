package sdk.utils;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于FFmpeg的高性能视频流读取器
 * 专门优化用于处理2K分辨率的Android设备视频流
 */
@Slf4j
public class FFmpegVideoStreamReader {

    private final InputStream inputStream;
    private final BlockingQueue<BufferedImage> frameQueue;
    private final AtomicBoolean isRunning;
    private final AtomicBoolean isReading;

    private FFmpegFrameGrabber grabber;
    private Java2DFrameConverter converter;
    private Thread readerThread;

    // 性能统计
    private long frameCount = 0;
    private long lastStatsTime = System.currentTimeMillis();
    private long totalBytesRead = 0;

    // 配置参数
    private static final int FRAME_QUEUE_SIZE = 30; // 帧队列大小
    private static final int STATS_INTERVAL = 5000; // 统计间隔(ms)

    public FFmpegVideoStreamReader(InputStream inputStream) {
        this.inputStream = inputStream;
        this.frameQueue = new LinkedBlockingQueue<>(FRAME_QUEUE_SIZE);
        this.isRunning = new AtomicBoolean(false);
        this.isReading = new AtomicBoolean(false);
        this.converter = new Java2DFrameConverter();
    }

    /**
     * 启动视频流读取
     */
    public boolean start() {
        if (isRunning.get()) {
            log.warn("FFmpeg视频流读取器已经在运行");
            return false;
        }

        return startWithRetry(3); // 最多重试3次
    }

    /**
     * 带重试机制的启动方法
     */
    private boolean startWithRetry(int maxRetries) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("FFmpeg启动尝试 {}/{}", attempt, maxRetries);

                // 检测视频流格式
                String detectedFormat = VideoStreamFormatDetector.detectVideoFormat(inputStream);
                log.info("检测到视频流格式: {}", detectedFormat);

                // 创建FFmpeg帧抓取器
                grabber = new FFmpegFrameGrabber(inputStream);

                // 根据尝试次数和检测到的格式调整配置
                configureGrabber(attempt, detectedFormat);

                // 启动抓取器（带超时控制）
                log.info("正在启动FFmpeg抓取器...");
                
                // 使用超时机制启动抓取器
                boolean startSuccess = startGrabberWithTimeout(30000); // 30秒超时
                if (!startSuccess) {
                    log.warn("FFmpeg启动超时，尝试 {}", attempt);
                    cleanup();

                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(2000 * attempt); // 递增延迟
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                    continue;
                }

                // 验证抓取器是否正确启动
                if (!isGrabberProperlyStarted()) {
                    log.warn("FFmpeg抓取器启动不完整，尝试 {}", attempt);
                    cleanup();
                    
                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(2000 * attempt);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                    continue;
                }

                // 获取视频流信息
                logVideoStreamInfo();

                isRunning.set(true);
                isReading.set(true);

                // 启动读取线程
                readerThread = new Thread(this::readFrames, "FFmpeg-VideoStream-Reader");
                readerThread.setDaemon(true);
                readerThread.start();

                log.info("FFmpeg视频流读取器启动成功 (尝试 {}), 格式: {}", attempt, detectedFormat);
                return true;

            } catch (Exception e) {
                log.warn("FFmpeg启动尝试 {} 失败: {}", attempt, e.getMessage());
                cleanup();

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(2000 * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("FFmpeg视频流读取器启动失败，已尝试 {} 次", maxRetries, e);
                }
            }
        }
        return false;
    }

    /**
     * 验证抓取器是否正确启动
     */
    private boolean isGrabberProperlyStarted() {
        try {
            if (grabber == null) {
                return false;
            }
            
            // 尝试获取基本信息，如果成功说明启动正常
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            
            log.info("FFmpeg抓取器验证: 宽度={}, 高度={}", width, height);
            
            return width > 0 && height > 0;
            
        } catch (Exception e) {
            log.warn("FFmpeg抓取器验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 带超时控制的抓取器启动方法
     */
    private boolean startGrabberWithTimeout(long timeoutMs) {
        Thread startThread = new Thread(() -> {
            try {
                log.info("开始调用FFmpeg grabber.start()...");
                grabber.start();
                log.info("FFmpeg grabber.start() 调用完成");
            } catch (Exception e) {
                log.error("FFmpeg抓取器启动异常: {}", e.getMessage(), e);
            }
        });
        
        startThread.start();
        
        try {
            startThread.join(timeoutMs);
            boolean success = !startThread.isAlive();
            log.info("FFmpeg启动线程状态: alive={}, success={}", startThread.isAlive(), success);
            return success;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            startThread.interrupt();
            log.warn("FFmpeg启动被中断");
            return false;
        }
    }

    /**
     * 配置FFmpeg抓取器
     */
    private void configureGrabber(int attempt, String detectedFormat) {
        String ffmpegFormat = VideoStreamFormatDetector.getFFmpegFormat(detectedFormat);
        
        if (attempt == 1) {
            // 第一次尝试：针对检测到的格式进行配置
            grabber.setOption("probesize", "4194304");   // 4MB，增加探测大小
            grabber.setOption("analyzeduration", "5000000"); // 5秒分析时间
            grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt");
            grabber.setOption("flags", "low_delay");
            grabber.setOption("strict", "experimental");
            grabber.setOption("threads", "auto");
            grabber.setOption("thread_type", "slice");
            grabber.setOption("rtbufsize", "4096000"); // 4MB缓冲区
            grabber.setOption("max_delay", "0");
            grabber.setOption("tune", "zerolatency");
            grabber.setOption("preset", "ultrafast");
            // 针对原始流的特殊配置
            grabber.setOption("fpsprobesize", "30");
            grabber.setOption("reorder_queue_size", "0");
            grabber.setOption("max_interleave_delta", "0");
            grabber.setOption("err_detect", "ignore_err");
            
            // H.264特殊配置，处理不完整的流
            if ("h264".equals(ffmpegFormat) || "h264_incomplete".equals(detectedFormat)) {
                grabber.setOption("skip_frame", "nokey");
                grabber.setOption("skip_loop_filter", "all");
                grabber.setOption("flags2", "export_mvs");
                grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt+igndts");
                // 尝试处理不完整的H.264流
                grabber.setOption("err_detect", "ignore_err");
                grabber.setOption("max_error_rate", "0.0");
                
                // 对于不完整的H.264流，使用更宽松的配置
                if ("h264_incomplete".equals(detectedFormat)) {
                    log.warn("检测到不完整的H.264流，使用特殊配置");
                    grabber.setOption("probesize", "8388608");   // 8MB
                    grabber.setOption("analyzeduration", "10000000"); // 10秒
                    grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt+igndts");
                    grabber.setOption("skip_frame", "nokey");
                    grabber.setOption("skip_loop_filter", "all");
                    grabber.setOption("avoid_negative_ts", "make_zero");
                    // 增加H.264解码器的容错性
                    grabber.setOption("h264_flags", "loop_filter");
                    grabber.setOption("h264_profile", "baseline");
                    grabber.setOption("h264_level", "3.0");
                }
            }
            
            // 根据检测到的格式设置参数
            if (!"auto".equals(ffmpegFormat)) {
                try {
                    grabber.setFormat(ffmpegFormat);
                    log.info("设置FFmpeg格式为: {}", ffmpegFormat);
                } catch (Exception e) {
                    log.warn("设置FFmpeg格式失败: {}, 错误: {}", ffmpegFormat, e.getMessage());
                }
            }
            
        } else if (attempt == 2) {
            // 第二次尝试：更宽松的配置，尝试其他格式
            grabber.setOption("probesize", "8388608");   // 8MB
            grabber.setOption("analyzeduration", "10000000"); // 10秒
            grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt+igndts");
            grabber.setOption("flags", "low_delay");
            grabber.setOption("strict", "experimental");
            grabber.setOption("threads", "auto");
            grabber.setOption("thread_type", "slice");
            grabber.setOption("rtbufsize", "8192000"); // 8MB缓冲区
            grabber.setOption("max_delay", "0");
            grabber.setOption("tune", "zerolatency");
            grabber.setOption("preset", "ultrafast");
            // 更宽松的配置
            grabber.setOption("fpsprobesize", "50");
            grabber.setOption("reorder_queue_size", "0");
            grabber.setOption("max_interleave_delta", "0");
            grabber.setOption("err_detect", "ignore_err");
            grabber.setOption("skip_frame", "nokey");
            grabber.setOption("skip_loop_filter", "all");
            
            // 尝试不同的格式
            String alternativeFormat = "h264".equals(ffmpegFormat) ? "mpeg4" : "h264";
            try {
                grabber.setFormat(alternativeFormat);
                log.info("尝试替代格式: {}", alternativeFormat);
            } catch (Exception e) {
                log.warn("设置替代格式失败: {}, 错误: {}", alternativeFormat, e.getMessage());
            }
            
        } else {
            // 第三次尝试：最宽松的配置，自动检测格式
            grabber.setOption("probesize", "16777216");   // 16MB
            grabber.setOption("analyzeduration", "15000000"); // 15秒
            grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt+igndts+genpts");
            grabber.setOption("flags", "low_delay");
            grabber.setOption("strict", "experimental");
            grabber.setOption("threads", "auto");
            grabber.setOption("thread_type", "slice");
            grabber.setOption("rtbufsize", "16777216"); // 16MB缓冲区
            grabber.setOption("max_delay", "0");
            grabber.setOption("tune", "zerolatency");
            grabber.setOption("preset", "ultrafast");
            // 最宽松的配置
            grabber.setOption("fpsprobesize", "100");
            grabber.setOption("reorder_queue_size", "0");
            grabber.setOption("max_interleave_delta", "0");
            grabber.setOption("err_detect", "ignore_err");
            grabber.setOption("skip_frame", "nokey");
            grabber.setOption("skip_loop_filter", "all");
            grabber.setOption("avoid_negative_ts", "make_zero");
            // 不指定格式，让FFmpeg自动检测
            log.info("使用自动格式检测");
        }

        log.debug("FFmpeg配置 (尝试 {}): probesize={}, analyzeduration={}, format={}",
            attempt, grabber.getOption("probesize"), grabber.getOption("analyzeduration"), ffmpegFormat);
    }

    /**
     * 获取下一帧（非阻塞）
     */
    public BufferedImage getNextFrame() {
        return frameQueue.poll();
    }

    /**
     * 获取下一帧（阻塞，带超时）
     */
    public BufferedImage getNextFrame(long timeoutMs) {
        try {
            return frameQueue.poll(timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }

    /**
     * 获取队列中的帧数量
     */
    public int getQueueSize() {
        return frameQueue.size();
    }

    /**
     * 获取帧率统计
     */
    public double getCurrentFPS() {
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastStatsTime;
        if (timeDiff > 0) {
            return (frameCount * 1000.0) / timeDiff;
        }
        return 0.0;
    }

    /**
     * 停止视频流读取
     */
    public void stop() {
        if (!isRunning.get()) {
            return;
        }

        log.info("正在停止FFmpeg视频流读取器...");
        isRunning.set(false);
        isReading.set(false);

        // 等待读取线程结束
        if (readerThread != null && readerThread.isAlive()) {
            try {
                readerThread.interrupt();
                readerThread.join(3000); // 最多等待3秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        cleanup();
        log.info("FFmpeg视频流读取器已停止");
    }

    /**
     * 读取帧的主循环
     */
    private void readFrames() {
        log.info("FFmpeg帧读取线程开始");

        try {
            // 验证抓取器状态
            if (grabber == null) {
                log.error("FFmpeg抓取器为空，无法读取帧");
                return;
            }

            // 等待一段时间让抓取器完全初始化
            Thread.sleep(100);

            // H.264错误计数器
            int h264ErrorCount = 0;
            final int MAX_H264_ERRORS = 30; // 降低最大允许的连续错误数
            long lastErrorTime = 0;
            final long ERROR_RESET_INTERVAL = 5000; // 5秒后重置错误计数

            while (isReading.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 检查抓取器状态
                    if (!isGrabberRunning()) {
                        log.warn("FFmpeg抓取器状态异常，停止读取");
                        break;
                    }

                    // 从FFmpeg抓取帧
                    Frame frame = grabber.grab();
                    if (frame == null) {
                        log.info("视频流结束");
                        break;
                    }

                    // 只处理图像帧
                    if (frame.image != null) {
                        // 转换为BufferedImage
                        BufferedImage bufferedImage = converter.convert(frame);
                        if (bufferedImage != null) {
                            // 如果队列满了，移除最老的帧
                            if (frameQueue.remainingCapacity() == 0) {
                                frameQueue.poll(); // 移除最老的帧
                            }

                            // 添加新帧
                            if (frameQueue.offer(bufferedImage)) {
                                frameCount++;
                                h264ErrorCount = 0; // 重置错误计数
                                logStats();
                            }
                        }
                    }

                } catch (Exception e) {
                    if (isReading.get()) {
                        String errorMsg = e.getMessage();
                        long currentTime = System.currentTimeMillis();
                        
                        // 检查是否需要重置错误计数
                        if (currentTime - lastErrorTime > ERROR_RESET_INTERVAL) {
                            h264ErrorCount = 0;
                            lastErrorTime = currentTime;
                        }
                        
                        if (errorMsg != null && errorMsg.contains("not enough frames to estimate rate")) {
                            // 这是常见的警告，不需要记录为错误
                            log.debug("FFmpeg帧率估算警告: {}", errorMsg);
                        } else if (errorMsg != null && errorMsg.contains("No AVFormatContext")) {
                            // 这是关键错误，说明抓取器没有正确启动
                            log.error("FFmpeg抓取器未正确启动: {}", errorMsg);
                            break;
                        } else if (errorMsg != null && (errorMsg.contains("sps_id") || 
                                                        errorMsg.contains("PPS") || 
                                                        errorMsg.contains("decode_slice_header") ||
                                                        errorMsg.contains("non-existing PPS") ||
                                                        errorMsg.contains("no frame"))) {
                            // H.264解码错误，增加错误计数
                            h264ErrorCount++;
                            log.debug("H.264解码错误 ({}/{}): {}", h264ErrorCount, MAX_H264_ERRORS, errorMsg);
                            
                            // 如果连续错误过多，尝试重新配置
                            if (h264ErrorCount >= MAX_H264_ERRORS) {
                                log.warn("H.264解码错误过多，尝试重新配置抓取器");
                                if (attemptReconfigureGrabber()) {
                                    h264ErrorCount = 0; // 重置错误计数
                                    lastErrorTime = currentTime;
                                } else {
                                    log.error("重新配置抓取器失败，停止读取");
                                    break;
                                }
                            }
                        } else {
                            log.error("读取视频帧失败: {}", errorMsg);
                        }
                        // 短暂休眠后继续尝试
                        Thread.sleep(10);
                    }
                }
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("FFmpeg帧读取线程被中断");
        } catch (Exception e) {
            log.error("FFmpeg帧读取线程异常: {}", e.getMessage(), e);
        } finally {
            log.info("FFmpeg帧读取线程结束");
        }
    }

    /**
     * 尝试重新配置抓取器
     */
    private boolean attemptReconfigureGrabber() {
        try {
            log.info("尝试重新配置FFmpeg抓取器...");
            
            // 停止当前抓取器
            if (grabber != null) {
                try {
                    grabber.stop();
                    grabber.release();
                } catch (Exception e) {
                    log.warn("停止抓取器失败: {}", e.getMessage());
                }
            }
            
            // 重新创建抓取器
            grabber = new FFmpegFrameGrabber(inputStream);
            
            // 使用最宽松的配置来处理H.264错误
            grabber.setOption("probesize", "16777216");   // 16MB
            grabber.setOption("analyzeduration", "15000000"); // 15秒
            grabber.setOption("fflags", "nobuffer+fastseek+genpts+discardcorrupt+igndts+genpts");
            grabber.setOption("flags", "low_delay");
            grabber.setOption("strict", "experimental");
            grabber.setOption("err_detect", "ignore_err");
            grabber.setOption("skip_frame", "nokey");
            grabber.setOption("skip_loop_filter", "all");
            grabber.setOption("max_error_rate", "0.0");
            grabber.setOption("avoid_negative_ts", "make_zero");
            
            // 针对H.264错误的特殊配置
            grabber.setOption("h264_flags", "loop_filter");
            grabber.setOption("h264_profile", "baseline");
            grabber.setOption("h264_level", "3.0");
            grabber.setOption("threads", "auto");
            grabber.setOption("thread_type", "slice");
            
            // 尝试不同的格式，优先尝试MPEG4
            try {
                grabber.setFormat("mpeg4");
                log.info("重新配置为MPEG4格式");
            } catch (Exception e) {
                log.warn("设置MPEG4格式失败，尝试H.264");
                try {
                    grabber.setFormat("h264");
                    log.info("重新配置为H.264格式");
                } catch (Exception e2) {
                    log.warn("设置H.264格式失败，使用自动检测");
                }
            }
            
            // 重新启动
            grabber.start();
            
            log.info("FFmpeg抓取器重新配置成功");
            return true;
            
        } catch (Exception e) {
            log.error("重新配置FFmpeg抓取器失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查抓取器是否正在运行
     */
    private boolean isGrabberRunning() {
        try {
            if (grabber == null) {
                return false;
            }
            
            // 尝试获取抓取器状态
            // 如果能够获取宽度和高度，说明抓取器正常运行
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            
            return width > 0 && height > 0;
            
        } catch (Exception e) {
            log.debug("检查抓取器状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 记录性能统计
     */
    private void logStats() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastStatsTime >= STATS_INTERVAL) {
            double fps = getCurrentFPS();
            int queueSize = getQueueSize();

            log.info("FFmpeg视频流统计: {:.1f} fps, 队列大小: {}, 总帧数: {}",
                    fps, queueSize, frameCount);

            // 重置统计
            frameCount = 0;
            lastStatsTime = currentTime;
        }
    }

    /**
     * 记录视频流信息
     */
    private void logVideoStreamInfo() {
        try {
            if (grabber != null) {
                int width = grabber.getImageWidth();
                int height = grabber.getImageHeight();
                double frameRate = grabber.getFrameRate();
                String format = grabber.getFormat();

                log.info("FFmpeg视频流信息: 分辨率={}x{}, 帧率={:.1f}, 格式={}",
                    width, height, frameRate, format);

                // 如果帧率为0或未知，设置默认值
                if (frameRate <= 0) {
                    log.warn("无法检测视频流帧率，使用默认值30fps");
                }
            }
        } catch (Exception e) {
            log.debug("获取视频流信息失败: {}", e.getMessage());
        }
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (grabber != null) {
                grabber.stop();
                grabber.release();
                grabber = null;
            }

            if (converter != null) {
                converter.close();
                converter = null;
            }

            frameQueue.clear();

        } catch (Exception e) {
            log.warn("清理FFmpeg资源时出错: {}", e.getMessage());
        }
    }
}
