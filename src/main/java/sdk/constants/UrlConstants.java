package sdk.constants;

import common.constant.AppConstants;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 11:52
 * @description :
 * @modified By :
 * @since : 2022-4-12
 */
@Slf4j
public class UrlConstants {

    public static final String SERVER_URL = String.format(
            "http://%s:%d/%s",
            AppConstants.getBaseComputerIp(), AppConstants.SERVER_PORT, AppConstants.SERVER_NAME);

    public static final String sseUrlPath = String.format("%s/sse/subscribe/", SERVER_URL);

    public static String getSseUrl(String subscribeId) {
        return String.format("%s?subscribeId=%s", sseUrlPath, subscribeId);
    }

    public static class ServerUrls {
        public static final String SERVER_LOG_FILE = SERVER_URL + "/logfile";
        public static final String SERVER_VERIFY = SERVER_URL + "/verify";
        public static final String HEALTH = SERVER_URL + "/health";
        public static final String GENERATE_SECRET_KEY_FROM_SERVER = SERVER_VERIFY + "/generate";
        public static final String GET_SERVER_PID = HEALTH + "/serverPid";
    }

    public static class WebSocketUrls {
        public static final String EXECUTION_MONITOR_URL = String.format(
                "ws://localhost:%d/%s/monitor/execution",
                AppConstants.SERVER_PORT,
                AppConstants.SERVER_NAME);

        public static final String EXCEL_CASE_EXECUTION_MONITOR_URL = String.format(
                "ws://localhost:%d/%s/monitor/excelCaseExecution",
                AppConstants.SERVER_PORT,
                AppConstants.SERVER_NAME);
        public static final String LOG_MONITOR_URL = String.format(
                "ws://localhost:%d/%s/monitor/printLog",
                AppConstants.SERVER_PORT,
                AppConstants.SERVER_NAME);

        public static final String TEXT_MONITOR_URL = String.format(
                "ws://localhost:%d/%s/ws",
                AppConstants.SERVER_PORT,
                AppConstants.SERVER_NAME);
        public static final String REMOTE_MONITOR_URL = String.format(
                "ws://localhost:%d/%s/remoteMonitor/websocket",
                AppConstants.SERVER_PORT,
                AppConstants.SERVER_NAME);
    }

    public static class OperationGroupUrls {
        public static final String BASE_URL = SERVER_URL + "/operationGroup";
        public static final String GET_ALL_OPERATION_GROUP = BASE_URL + "/all";
        public static final String ADD_OPERATION_GROUP = BASE_URL + "/add";
        public static final String UPDATE_OPERATION_GROUP = BASE_URL + "/update";
        public static final String LOAD_ALL_OPERATION_GROUPS = BASE_URL + "/load/all";
        public static final String LOAD_OPERATION_GROUP = BASE_URL + "/load";
        public static final String DEL_TEST_SCRIPT_FILE = BASE_URL + "/delete";

        public static String get_delete_operationGroup_url(String groupName) {
            return String.format("%s/%s", DEL_TEST_SCRIPT_FILE, groupName);
        }
    }

    public static class TestScriptUrls {
        public static final String BASE_URL = SERVER_URL + "/testScript";
        public static final String CLEAR_ALL_TEST_SCRIPT_FILE = BASE_URL + "/clear";
        public static final String GET_ALL_TEST_SCRIPT_FILE = BASE_URL + "/all";
        public static final String ADD_TEST_SCRIPT_FILE = BASE_URL + "/add";
        public static final String DEL_TEST_SCRIPT_FILE = BASE_URL + "/delete";
        public static final String RENAME_TEST_SCRIPT_FILE = BASE_URL + "/rename";
        public static final String UPDATE_TEST_SCRIPT_FILE = BASE_URL + "/update";
        public static final String SELECT_TEST_SCRIPT_FILE = BASE_URL + "/update/selectAll";
        public static final String UPDATE_TEST_SCRIPT_FILE_CONTENT = BASE_URL + "/update/file";
        public static final String UPDATE_TEST_SCRIPT_TEST_CYCLE = BASE_URL + "/update/testCycle";
        public static final String LOAD_TEST_SCRIPT_FILE = BASE_URL + "/load";

        public static String get_load_testScript_url(String testScriptFileUUID) {
            return String.format("%s/%s", LOAD_TEST_SCRIPT_FILE, testScriptFileUUID);
        }

        public static String get_delete_testScript_url(String testScriptFileUUID) {
            return String.format("%s/%s", DEL_TEST_SCRIPT_FILE, testScriptFileUUID);
        }
    }

    public static class ActionSequenceUrls {
        public static final String BASE_URL = SERVER_URL + "/actionSequence";
        public static final String CHECK_ACTION_SEQUENCE = BASE_URL + "/check";
        public static final String CHECK_ADN_EXECUTE_ACTION_SEQUENCE = BASE_URL + "/checkAndExecute";
        public static final String EXECUTE_ACTION_SEQUENCE = BASE_URL + "/execute";
        public static final String PREPARE_EXECUTE_ACTION_SEQUENCE = BASE_URL + "/prepareExecute";
        public static final String FAIL_ACTION_SEQUENCE = BASE_URL + "/failExecute";
        public static final String PREPARE_ACTION_SEQUENCE_SSE = BASE_URL + "/prepareSseMonitor";
        public static final String PAUSE_ACTION_SEQUENCE = BASE_URL + "/pauseExecute";
        public static final String RESUME_ACTION_SEQUENCE = BASE_URL + "/resumeExecute";
        public static final String STOP_ACTION_SEQUENCE = BASE_URL + "/stopExecute";
        public static final String COMPLETE_ACTION_SEQUENCE = BASE_URL + "/completeExecute";
        public static final String CONVERT_ACTION_SEQUENCE = BASE_URL + "/convert";
        public static final String CLOSE_ALL_SSE = BASE_URL + "/closeAllSSE";
        public static final String MAIL_TEST_RESULT = BASE_URL + "/mailTestResult";
        public static final String ROBOT_TEST_RESULT = BASE_URL + "/robotTestResult";
        public static final String CLOUD_DOC_TEST_RESULT = BASE_URL + "/cloudDocTestResult";
        public static final String ROBOT_UPGRADE_RESULT = BASE_URL + "/robotUpgradeResult";
        public static final String MAIL_UPGRADE_RESULT = BASE_URL + "/mailUpgradeResult";
        public static final String CHECK_ADN_EXECUTE_SINGLE_ACTION_SEQUENCE = BASE_URL + "/checkAndExecuteSingleTest";
        public static final String STOP_SINGLE_ACTION_SEQUENCE = BASE_URL + "/stopExecuteSingleTest";

        /**
         * 更新测试配置
         */
        public static final String UPDATE_TEST_CONFIG = BASE_URL + "/updateTestConfig";

        /**
         * 读取测试配置
         */
        public static final String READ_TEST_CONFIG = BASE_URL + "/readTestConfig/%s";
    }

    public static class ConfigUrls {
        public static final String BASE_URL = SERVER_URL + "/config";
        public static final String UPDATE_GLOBAL_CONFIG = BASE_URL + "/global/update";
        public static final String LOAD_GLOBAL_CONFIG = BASE_URL + "/global/load";

        public static final String GET_MOCK_ENABLED = BASE_URL + "/mock/mockEnabled";
        public static final String SET_MOCK_ENABLED = BASE_URL + "/mock/mockEnabled";

        public static final String SAVE_CONFIG = BASE_URL + "/save";
        public static final String GET_CONFIG = BASE_URL + "/get";
    }

    public static class LightTestBoxConfigUrls {
        public static final String BASE_URL = SERVER_URL + "/lightTestBoxConfig";
        public static final String READ_JSON_CONFIG_FILE = BASE_URL + "/readJsonConfigFile";
        public static final String LOAD_EXCEL_CONFIG_FILE = BASE_URL + "/loadConfigFile";
        public static final String PARSE_CONFIG_FILE = BASE_URL + "/parseConfig";
    }

    public static class FdxUrls {
        public static final String BASE_URL = SERVER_URL + "/fdxConfig";
        public static final String READ_CONFIG_FILE = BASE_URL + "/readFdxFile";
        public static final String PARSE_CONFIG = BASE_URL + "/parse";
    }

    public static class UdpUrls {
        public static final String BASE_URL = SERVER_URL + "/udpConfig";
        public static final String LOAD_A2L_CONFIG_FILE = BASE_URL + "/loadA2LConfig";
        public static final String PARSE_A2L_CONFIG_FILE = BASE_URL + "/parseA2LFile";
    }

    public static class ExcelUrls {
        //        public static final String BASE_URL = SERVER_URL + "/excel";
        public static final String BASE_URL = SERVER_URL + "/excelcase";
        public static final String LOAD_EXCEL = BASE_URL + "/loadExcel";
        public static final String LOAD_DB_TEST_CASE = BASE_URL + "/loadDBTestCase";
        public static final String SYNC_COLUMN_CONSTANTS_TEMPLATE = BASE_URL + "/syncColumnConstantsTemplate";
        public static final String INSERT_EXCEL_CASE_DATA = BASE_URL + "/insertExcelCaseData";
        public static final String GET_SHEET_NAMES = BASE_URL + "/sheetNames";
        public static final String EXPORT_EXCEL_CASE_REPORT = BASE_URL + "/exportExcelCase";
        public static final String FIND_EXCEL_CASE_LIST = BASE_URL + "/findExcelCaseByTableName";
        public static final String FIND_EXCEL_CASE_ROW = BASE_URL + "/findExcelCaseInfoByUuid";
        public static final String UPDATE_EXCEL_CASE = BASE_URL + "/updateExcelCase";
        public static final String UPDATE_ORDER_EXCEL_CASE = BASE_URL + "/updateOrderExcelCase";
        public static final String DELETE_EXCEL_CASE_TABLE = BASE_URL + "/deleteExcelCaseTable";
        public static final String CLEAR_MAP_COLUMN_DATA = BASE_URL + "/clearMapColumnData";

    }

    public static class ClientUrls {
        public static final String SERVER_URL_OF_CLIENT_MGMT = SERVER_URL + "/client";
        public static final String REGISTER_CLIENT_URL = SERVER_URL_OF_CLIENT_MGMT + "/register";
        public static final String USER_LOGIN_URL = SERVER_URL_OF_CLIENT_MGMT + "/login";
        public static final String USER_LOGOUT_URL = SERVER_URL_OF_CLIENT_MGMT + "/logout";
        public static final String EXIT_CLIENT_URL = SERVER_URL_OF_CLIENT_MGMT + "/exit";
        public static final String CONFIGURATION_EMAILS = SERVER_URL_OF_CLIENT_MGMT + "/configurationEmails";
        public static final String CONFIGURATION_SCRIPT_EMAILS = SERVER_URL_OF_CLIENT_MGMT + "/configurationScriptEmails";
        public static final String CONFIGURATION_ROBOT_URLS = SERVER_URL_OF_CLIENT_MGMT + "/configurationRobotUrls";
    }

    public static class RelayUrls {
        public static final String SERVER_URL_OF_RELAY_MGMT = SERVER_URL + "/relay";
        public static final String UPDATE_RELAY_DELAY_CONFIG = SERVER_URL_OF_RELAY_MGMT + "/updateRelayDelayConfig";
        public static final String UPDATE_RELAY_TEXT_CONFIG = SERVER_URL_OF_RELAY_MGMT + "/updateRelayTextConfig";
        public static final String UPDATE_RELAY_STATUS_CONFIG = SERVER_URL_OF_RELAY_MGMT + "/updateRelayStatusConfig";
        public static final String UPDATE_CONNECT_SWITCH_STATUS = SERVER_URL_OF_RELAY_MGMT + "/updateConnectSwitchStatus";
        public static final String GET_RELAY_TEXT_CONFIG = SERVER_URL_OF_RELAY_MGMT + "/getRelayTextConfig";
    }

    public static class ScreenUrls {
        public static final String SERVER_URL_OF_SCREEN_MGMT = SERVER_URL + "/screen";
        public static final String LOAD_SCREEN_CONFIG = SERVER_URL_OF_SCREEN_MGMT + "/loadConfig";
        public static final String UPDATE_SCREEN_CONFIG = SERVER_URL_OF_SCREEN_MGMT + "/updateConfig";
        public static final String PARSE_TOUCH_POINT = SERVER_URL_OF_SCREEN_MGMT + "/parseTouchPoint";
    }

    public static class CameraConfigUrls {
        public static final String SERVER_URL_OF_CAMERA_MGMT = SERVER_URL + "/camera";
        public static final String LOAD_CAMERA_CONFIG = SERVER_URL_OF_CAMERA_MGMT + "/loadConfig";
        public static final String UPDATE_CAMERA_CONFIG = SERVER_URL_OF_CAMERA_MGMT + "/updateConfig";

    }

    public static class MonitorUrls {

        public static final String SERVER_URL_OF_MONITOR_MGMT = SERVER_URL + "/monitor";

//        public static String monitor(String deviceAliasName, String monitorType) {
//            return SERVER_URL_OF_MONITOR_MGMT + "/" + deviceAliasName + "/" + monitorType;
//        }
    }

    public static class DeviceUrls {
        public static final String SERVER_URL_OF_DEVICE_MGMT = SERVER_URL + "/device";
        public static final String REGISTER_DEVICE_URL = SERVER_URL_OF_DEVICE_MGMT + "/register";
        public static final String REGISTER_AND_OPEN_DEVICE_URL = SERVER_URL_OF_DEVICE_MGMT + "/registerAndOpen";
        public static final String UNREGISTER_DEVICE_URL = SERVER_URL_OF_DEVICE_MGMT + "/unregister";
        public static final String DISCONNECT_DEVICE_URL = SERVER_URL_OF_DEVICE_MGMT + "/disconnect";
        public static final String UNREGISTER_ALL_DEVICES = SERVER_URL_OF_DEVICE_MGMT + "/unregister/all";
        public static final String GET_ALL_DEVICES_URL = SERVER_URL_OF_DEVICE_MGMT + "/allDevices";
        public static final String QUERY_DEVICES_BY_TYPE_URL = SERVER_URL_OF_DEVICE_MGMT + "/deviceType";
        public static final String GET_DEVICE = SERVER_URL_OF_DEVICE_MGMT + "/deviceName";
        public static final String GET_CAN_DEVICE_DBC = SERVER_URL_OF_DEVICE_MGMT + "/dbcFiles";
        public static final String GET_TESTBOX_CONFIG_FILE = SERVER_URL_OF_DEVICE_MGMT + "/testBoxConfig";

        public static String getUnregisterDeviceUrl(String deviceName) throws UnsupportedEncodingException {
            String encodedDeviceName = URLEncoder.encode(deviceName, String.valueOf(StandardCharsets.UTF_8));
            return String.format("%s?deviceName=%s", UNREGISTER_DEVICE_URL, encodedDeviceName);
        }

        public static String getDisconnectDeviceUrl(String deviceName) throws UnsupportedEncodingException {
            String encodedDeviceName = URLEncoder.encode(deviceName, String.valueOf(StandardCharsets.UTF_8));
            return String.format("%s?deviceName=%s", DISCONNECT_DEVICE_URL, encodedDeviceName);
        }

        public static String getQueryDeviceByTypeUrl(String deviceType) {
            return String.format("%s/%s", QUERY_DEVICES_BY_TYPE_URL, deviceType);
        }

        public static String getQueryDeviceByNameUrl(String deviceName) throws UnsupportedEncodingException {
            return String.format("%s/%s", GET_DEVICE, URLEncoder.encode(deviceName, "UTF-8"));
        }

        public static class CameraUrls {
            public static final String BASE_CAMERA_RTSP_URL = "rtsp://localhost:8554/camera";
            public static final String ALL_PHYSICAL_CAMERAS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allPhysicalCameras";

            public static String getAllPhysicalCameras(String deviceModel) {
                if (deviceModel == null) {
                    return ALL_PHYSICAL_CAMERAS;
                }
                return ALL_PHYSICAL_CAMERAS + "/" + deviceModel;
            }

            public static String getCameraRtspUrl(String deviceModel, int devicePort) {
                return String.format("%s/%s/%d", BASE_CAMERA_RTSP_URL, deviceModel, devicePort);
            }
        }

        public static class StreamUrls {
            public static final String DEVICE_STREAM = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/stream";
            public static final String COMMAND_OF_GET_DEFAULT_SIZE = "getDefaultSize";
            public static final String COMMAND_OF_SYNC = "sync";

            public static String getControlCommandUrl(String command) {
                return String.format("%s/%s", DEVICE_STREAM, command);
            }

        }

        public static class PhysicalPortUrls {
            public static final String ALL_PHYSICAL_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allSerialPorts";
            public static final String ALL_VISA_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allVisaPorts";
            public static final String ALl_CAN_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allCanPorts";
            public static final String ALL_PHYSICAL_IP_ROBOTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allIpRobots";
            public static final String ALL_ANDROID_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allAndroids";
            public static final String ALL_QNX_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allQnxDevices";
            public static final String ALl_AUTO_CLICKER_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allAutoClicker";
            public static final String ALL_DAQ_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allDaq";
            public static final String ALl_USB_I2C_PORTS = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allUsbI2C";
            public static final String ALL_REMOTE_RESISTANCE_IP = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allRemoteResistance";
            public static final String ALL_TCP_SERVER = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allTcpServer";
            public static final String ALL_TCP_CLIENT = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allTcpClient";
            public static final String ALL_SOUND_DEVICE = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allSoundDevices";
            public static final String ALL_USB4704_DEVICE = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allUsb4704Devices";
            public static final String ALL_SPEAKER_DEVICE = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allSpeakerDevices";
            public static final String ALL_VIDEO_CAPTURE_DEVICE = DeviceUrls.SERVER_URL_OF_DEVICE_MGMT + "/allVideoCaptureDevices";

            public static String getAllAndroids(String deviceModel) {
                if (deviceModel == null) {
                    return ALL_ANDROID_PORTS;
                }
                return ALL_ANDROID_PORTS + "/" + deviceModel;
            }

            public static String getAllCanPorts(String deviceModel, Integer deviceChannel) {
                return ALl_CAN_PORTS + "/" + deviceModel + "/" + deviceChannel;
            }

            public static String getAllVisaPorts(String deviceModel) {
                return ALL_VISA_PORTS + "/" + deviceModel;
            }

            public static String getAllDaqPorts(String deviceModel) {
                return ALL_DAQ_PORTS + "/" + deviceModel;
            }

            public static String getAllSerialPorts(String deviceModel) {
                return ALL_PHYSICAL_PORTS + "/" + deviceModel;
            }

            public static String getAllIpRobotPorts(String deviceModel) {
                return ALL_PHYSICAL_IP_ROBOTS + "/" + deviceModel;
            }

            public static String getAllAutoClicker(String deviceModel) {
                return ALl_AUTO_CLICKER_PORTS + "/" + deviceModel;
            }

            public static String getAllTcpServer(String deviceModel) {
                return ALL_TCP_SERVER + "/" + deviceModel;
            }

            public static String getAllTcpClient(String deviceModel) {
                return ALL_TCP_SERVER + "/" + deviceModel;
            }

            public static String getAllUsbI2c(String deviceModel) {
                return ALl_USB_I2C_PORTS + "/" + deviceModel;
            }

            public static String getAllRemoteResistanceIp(String deviceModel) {
                return ALL_REMOTE_RESISTANCE_IP + "/" + deviceModel;
            }

            public static String getAllSoundDevices(String deviceModel) {
                return ALL_SOUND_DEVICE + "/" + deviceModel;
            }

            public static String getAllUsb4704(String deviceModel) {
                return ALL_USB4704_DEVICE + "/" + deviceModel;
            }

            public static String getAllSpeakerDevices(String deviceModel) {
                return ALL_SPEAKER_DEVICE + "/" + deviceModel;
            }

            public static String getAllVideoCaptureDevices(String deviceModel) {
                return ALL_VIDEO_CAPTURE_DEVICE + "/" + deviceModel;
            }
        }

    }

    public static class ImageUrls {
        public static final String SERVER_URL_OF_IMAGE_MGMT = SERVER_URL + "/image";
        public static final String UPLOAD_TEMPLATE = SERVER_URL_OF_IMAGE_MGMT + "/saveTemplate";
        public static final String QUERY_TEMPLATE = SERVER_URL_OF_IMAGE_MGMT + "/queryTemplate";
        public static final String UPLOAD_TEMPLATE_ROI = SERVER_URL_OF_IMAGE_MGMT + "/saveTemplateROI";
    }

    public static class ProjectUrls {
        public static final String SERVER_URL_OF_PROJECT_MGMT = SERVER_URL + "/project";
        public static final String GET_PUBLIC_PROJECT = SERVER_URL_OF_PROJECT_MGMT + "/publicProject";
        public static final String REGISTER_PROJECT = SERVER_URL_OF_PROJECT_MGMT + "/register";
        public static final String GET_ALL_PROJECTS = SERVER_URL_OF_PROJECT_MGMT + "/all";

    }

    public static class TemplateRoiUrls {
        public static final String SERVER_URL_OF_TEMPLATE_ROI_MGMT = SERVER_URL + "/templateRoi";
        public static final String GET_RECT_ROI_TYPE = SERVER_URL_OF_TEMPLATE_ROI_MGMT + "/roiType/rect";
        public static final String GET_RECT_CIRCLE_TYPE = SERVER_URL_OF_TEMPLATE_ROI_MGMT + "/roiType/circle";
        public static final String GET_ROI = SERVER_URL_OF_TEMPLATE_ROI_MGMT + "/roi";
        public static final String GET_ALL_ROI = SERVER_URL_OF_TEMPLATE_ROI_MGMT + "/allRoi";
    }

    public static class TestMgmtUrls {
        public static final String SERVER_URL_OF_TEST_MGMT = SERVER_URL + "/test";
        public static final String START_EXECUTE = SERVER_URL_OF_TEST_MGMT + "/execute/start";
        public static final String START_DEBUG = SERVER_URL_OF_TEST_MGMT + "/execute/debug";
        public static final String STOP_DEBUG = SERVER_URL_OF_TEST_MGMT + "/execute/stopDebug";
        public static final String RESUME_EXECUTE = SERVER_URL_OF_TEST_MGMT + "/execute/resume";
        public static final String NEXT_EXECUTE = SERVER_URL_OF_TEST_MGMT + "/execute/next";
        public static final String STOP_EXECUTE = SERVER_URL_OF_TEST_MGMT + "/execute/stop";
        public static final String PAUSE_EXECUTE = SERVER_URL_OF_TEST_MGMT + "/execute/pause";
        public static final String EXECUTE_OF_IMAGE = START_EXECUTE + "/image";
        public static final String EXECUTE_OF_DEVICE = START_EXECUTE + "/device";
        public static final String OPERATE = SERVER_URL_OF_TEST_MGMT + "/operate";
        public static final String OPERATE_OF_DEVICE = OPERATE + "/device";

        @SneakyThrows
        public static String getOpenDeviceUrl(String deviceName) {
            return String.format("%s/open?deviceName=%s", OPERATE_OF_DEVICE, URLEncoder.encode(deviceName, "UTF-8"));
        }

        @SneakyThrows
        public static String getAutoOpenDeviceUrl(String deviceName) {
            return String.format("%s/autoOpen?deviceName=%s", OPERATE_OF_DEVICE, URLEncoder.encode(deviceName, "UTF-8"));
        }


        @SneakyThrows
        public static String getCloseDeviceUrl(String deviceName) {
            return String.format("%s/close?deviceName=%s", OPERATE_OF_DEVICE, URLEncoder.encode(deviceName, "UTF-8"));
        }

        @SneakyThrows
        public static String getSendDeviceUrl(String deviceName) {
            return String.format("%s/send?deviceName=%s", OPERATE_OF_DEVICE, URLEncoder.encode(deviceName, "UTF-8"));
        }
    }

    public static class DataMonitorUrls {
        public static final String MONITOR_CENTER = String.format("ws://localhost:%d/%s/monitor",
                AppConstants.SERVER_PORT, AppConstants.SERVER_NAME);

        public static String getDeviceDataMonitorUrl(String monitorType, String deviceAliasName) {
            try {
                return String.format("%s/device/%s/%s", MONITOR_CENTER, monitorType,
                        URLEncoder.encode(deviceAliasName, StandardCharsets.UTF_8.toString()));
            } catch (UnsupportedEncodingException e) {
                log.error(e.getMessage(), e);
                return String.format("%s/device/%s/%s", MONITOR_CENTER, monitorType, deviceAliasName);
            }
        }
    }

    public static class RobotCoordinatesUrls {
        public static final String ROBOT_COORDINATES_URL = SERVER_URL + "/robotCoordinates";
        public static final String ADD_FUNCTIONAL_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/addFunction";
        public static final String UPDATE_FUNCTIONAL_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/updateFunction";
        public static final String QUERY_FUNCTIONAL_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/queryFunction";
        public static final String ADD_ROBOT_ROI = ROBOT_COORDINATES_URL + "/addRoi";
        public static final String DELETE_ROBOT_ROI = ROBOT_COORDINATES_URL + "/deleteRoi";
        public static final String GET_ROBOT_ROI = ROBOT_COORDINATES_URL + "/roi";
        public static final String ADD_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/add";
        public static final String DELETE_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/delete";
        public static final String CLEAR_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/clear";
        public static final String UPDATE_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/update";
        public static final String GET_ALL_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/all";
        public static final String SWIPE_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/swipe";
        public static final String EXECUTE_SAFE_POINT = ROBOT_COORDINATES_URL + "/executeSafePoint";
        public static final String LONG_TOUCH_ROBOT_COORDINATES = ROBOT_COORDINATES_URL + "/longTouch";

    }

    public static class PolyTestAPIUrls {
        public static final String BASE_URL = "http://***********:8019";
        public static final String LOGIN_VERIFY = BASE_URL + "/polytest/user/login";
        public static final String GET_BU_LIST_URL = BASE_URL + "/polytest/configmgt/getBuList";
        public static final String GET_CAR_FAC_LIST_URL = BASE_URL + "/polytest/configmgt/getCarFacList";
        public static final String GET_PROJECT_LIST_BY_DEPT_ID_AD_CAR_FAC_URL = BASE_URL + "/polytest/configmgt/getProjectListByDeptIdAdCarFac";
    }

    public static class TestSuiteUrls {
        public static final String SERVER_URL_OF_TEST_SUITE = SERVER_URL + "/test";
        public static final String UPLOAD_TEST_SUITE_INFO = SERVER_URL_OF_TEST_SUITE + "/uploadTestSuiteInfo";
        public static final String UPLOAD_TEST_CASE_EXECUTE_INFO = SERVER_URL_OF_TEST_SUITE + "/uploadTestCaseExecuteInfo";
        public static final String UPLOAD_MANUALLY_TEST_INFO = SERVER_URL_OF_TEST_SUITE + "/uploadManuallyTestInfo";

    }

    public static class TestCaseUrls {
        public static final String SERVER_URL_OF_TEST_CASE = SERVER_URL + "/testcase";

        public static final String QUERY_TEST_RESULT = SERVER_URL_OF_TEST_CASE + "/testresult";

        public static String getQueryTestResultUrl(String testcaseUUID) {
            return String.format("%s/%s", QUERY_TEST_RESULT, testcaseUUID);
        }
    }

    public static class SmokingTestUrls {
        public static final String UPGRADE_URL = SERVER_URL + "/upgrade";
        public static final String SMOKING_TEST_CONFIG = UPGRADE_URL + "/smokingTestConfig";
        public static final String START_UPGRADE = UPGRADE_URL + "/start";
        public static final String CLOSE_UPGRADE_MONITOR = UPGRADE_URL + "/closeUpgradeMonitor";
    }


    public static class SoundDeviceUrls {
        public static final String UPGRADE_URL = SERVER_URL + "/upgrade";

    }

    public static class AllConnectedDevicesUrls {
        public static final String BASE_URL = SERVER_URL + "/allConnectedDevices";
        public static final String CAMERA_URL = BASE_URL + "/allCameras";
        public static final String CAPTURE_URL = BASE_URL + "/allCaptures";
        public static final String QNX_INSTRUMENT_URL = BASE_URL + "/allQnxInstruments";
        public static final String ANDROID_URL = BASE_URL + "/allAndroids";
        public static final String USB_SWITCH_URL = BASE_URL + "/allUsbSwitches";
    }

    public static class AndroidScreencapUrls {
        public static final String BASE_URL = SERVER_URL + "/android";
        public static final String STREAM_URL = BASE_URL + "/stream/video";
        public static final String STOP_STREAM_URL = BASE_URL + "/stream/video/stop";

        /**
         * 获取Android设备视频流URL（默认参数）
         *
         * @param deviceName 设备名称
         * @return 视频流URL
         */
        public static String getVideoStreamUrl(String deviceName) {
            try {
                String encodedDeviceName = URLEncoder.encode(deviceName, StandardCharsets.UTF_8.toString());
                return String.format("%s/%s", STREAM_URL, encodedDeviceName);
            } catch (UnsupportedEncodingException e) {
                log.error("编码设备名称失败: {}", deviceName, e);
                return String.format("%s/%s", STREAM_URL, deviceName);
            }
        }

        public static String getStopVideoStreamUrl(String deviceName) {
            try {
                String encodedDeviceName = URLEncoder.encode(deviceName, StandardCharsets.UTF_8.toString());
                return String.format("%s/%s", STOP_STREAM_URL, encodedDeviceName);
            } catch (UnsupportedEncodingException e) {
                log.error("编码设备名称失败: {}", deviceName, e);
                return String.format("%s/%s", STOP_STREAM_URL, deviceName);
            }
        }


        /**
         * 获取Android设备视频流URL（带参数）
         *
         * @param deviceName 设备名称
         * @param width      视频宽度（0表示使用默认值）
         * @param height     视频高度（0表示使用默认值）
         * @param bitRate    视频比特率（0表示使用默认值）
         * @return 视频流URL
         */
        public static String getVideoStreamUrlWithParams(String deviceName, int width, int height, int bitRate) {
            try {
                String encodedDeviceName = URLEncoder.encode(deviceName, StandardCharsets.UTF_8.toString());
                return String.format("%s/%s/params?width=%d&height=%d&bitRate=%d",
                        STREAM_URL, encodedDeviceName, width, height, bitRate);
            } catch (UnsupportedEncodingException e) {
                log.error("编码设备名称失败: {}", deviceName, e);
                return String.format("%s/%s/params?width=%d&height=%d&bitRate=%d",
                        STREAM_URL, deviceName, width, height, bitRate);
            }
        }
    }

    public static class HudDetectionTestUrls {
        public static final String HUD_URL = SERVER_URL + "/hudDetection";
    }

    public static class CanLogTestUrls {
        public static final String CAN_LOG_PARSE_URL = SERVER_URL + "/canLog/parse";
    }

    public static class CicdTestUrls {
        public static final String CICD_TEST_URL = SERVER_URL + "/cicdTest";
        //自动升级
        public static final String AUTO_UPGRADE = CICD_TEST_URL + "/autoUpgrade";
        //通知升级结果给云文档
        public static final String CLIENT_REPORT_UPGRADE_RESULT_TO_CLOUD_DC = CICD_TEST_URL + "/clientReportUpgradeResultToCloudDc";
    }
}
