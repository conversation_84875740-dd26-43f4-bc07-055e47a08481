package ui.model.report_point;

import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class ResolutionModel  implements ModelObservable, ResolutionObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();
    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void updateResolutionWidth(int width) {
        for (ModelObserver modelObserver : modelObservers) {
            ((ResolutionObserver) modelObserver).updateResolutionWidth(width);
        }
    }

    @Override
    public void updateResolutionHeight(int height) {
        for (ModelObserver modelObserver : modelObservers) {
            ((ResolutionObserver) modelObserver).updateResolutionHeight(height);
        }
    }
}
