package ui.layout.left.display.dialogs;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import common.utils.DateUtils;
import common.utils.ResourceUtils;
import common.utils.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseImportListener;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

@Getter
class ExcelDataListener extends AnalysisEventListener<ExcelData> {
    private final List<ExcelData> dataList = new ArrayList<>();

    @Override
    public void invoke(ExcelData excelData, AnalysisContext context) {
        if (excelData.getCommand() != null && !excelData.getCommand().trim().isEmpty()) {
            dataList.add(excelData);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }
}

@Slf4j
public class AdsScriptSwitchPanel extends JPanel {
    private final JButton transferButton;
    private final JButton importTestCaseButton;
    private JButton selectedButton;
    private JTextField filePathTextField;
    private final JTextArea tipTextArea;
    private final JScrollPane scrollPane;
    private final ExcelDataListener excelListener;
    private File targetFile;
    private ExcelCaseImportListener listener;

    public AdsScriptSwitchPanel(ExcelCaseImportListener listener) {
        this.listener = listener;
        excelListener = new ExcelDataListener();
        setLayout(new BorderLayout(0, 20));
        Box vBox = Box.createVerticalBox();
        vBox.add(createSelectFileBox());
        Box btnBox = Box.createHorizontalBox();
        transferButton = new JButton("开始转换");
        importTestCaseButton = new JButton("导入转换用例");
        importTestCaseButton.setEnabled(false);
        transferButton.setEnabled(false);
        btnBox.add( Box.createHorizontalGlue());
        btnBox.add(transferButton);
        btnBox.add(importTestCaseButton);
        btnBox.add( Box.createHorizontalGlue());
        add(vBox, BorderLayout.NORTH);
        // 初始化 tipTextArea
        tipTextArea = new JTextArea(" ");
        tipTextArea.setBackground(null);
        tipTextArea.setVisible(false);
        tipTextArea.setLineWrap(true); // 自动换行
        tipTextArea.setWrapStyleWord(true); // 按词换行
        // 包裹 tipTextArea 使其自适应
        scrollPane = new JScrollPane(tipTextArea);
        scrollPane.setVisible(false); // 初始设置为不可见，如果需要显示时再设置为 true
        add(scrollPane, BorderLayout.CENTER);
        add(btnBox, BorderLayout.SOUTH);
        createActions();
    }

    private Box createSelectFileBox() {
        Box selectFileBox = Box.createHorizontalBox();
        filePathTextField = new JTextField(40);
        selectedButton = new JButton("选择文件");
        selectFileBox.add(new JLabel("串口源文件路径："));
        selectFileBox.add(filePathTextField);
        selectFileBox.add(selectedButton);
        return selectFileBox;
    }

    private void displayTipText(String text) {
        if (text != null) {
            SwingUtil.invokeLater(() -> {
                tipTextArea.setVisible(true);
                scrollPane.setVisible(true);
                tipTextArea.setText(text);
            });
        }
    }

    private void createActions() {
        selectedButton.addActionListener(e -> {
            FileNameExtensionFilter filter = new FileNameExtensionFilter("Excel测试用例(.xlsx,.xls,.xlsm)", "xlsx", "xls", "xlsm");
            File selectedFile = SwingUtil.getFileChooser(this, "打开Excel表格", filter, new File(System.getProperty("user.home")), false);
            if (selectedFile == null) {
                return;
            }
            filePathTextField.setText(selectedFile.getAbsolutePath());
            transferButton.setEnabled(true);
        });
        filePathTextField.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                transferButton.setEnabled(true);
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                transferButton.setEnabled(true);

            }
        });
        transferButton.addActionListener(e -> {
            if (filePathTextField.getText().trim().isEmpty()) {
                SwingUtil.showWarningDialog(null, "ADS串口源文件路径不能为空");
            } else {
                startFormatConversion(StringUtils.trimBothEndsQuotation(filePathTextField.getText().trim()));
            }
        });
        importTestCaseButton.addActionListener(e -> importTestCase());
    }

    private void importTestCase() {
        if (targetFile == null)
            return;
        listener.importExcelCase(targetFile);
    }


    private void startFormatConversion(String filePath) {
        CustomProgressDialog progressDialog = new CustomProgressDialog(null, "文件转换进度");
        SwingWorker<File, Integer> worker = new SwingWorker<File, Integer>() {
            @Override
            protected File doInBackground() throws Exception {
                long startTime = System.currentTimeMillis();
                setProgress(0);  // 开始处理
                File targetFile = transferTestCase(filePath);
                setProgress(100);  // 完成处理
                log.info("转换后的ADS文件处理完成，耗时: {}毫秒", System.currentTimeMillis() - startTime);
                return targetFile;
            }

            @Override
            protected void done() {
                try {
                    targetFile = get(); // 将 targetFile 赋值给成员变量
                    displayTipText(targetFile.getAbsolutePath());
                    importTestCaseButton.setEnabled(true);
                } catch (Exception ex) {
                    displayTipText("文件转换失败: " + ex.getCause().getMessage());
                } finally {
                    progressDialog.setVisible(false);
                    transferButton.setEnabled(true);
                }
            }
        };
        // 设置进度对话框
        worker.addPropertyChangeListener(progressDialog);

        // 更新UI并开始任务
        tipTextArea.setText("");
        transferButton.setEnabled(false);

        // 使用 invokeLater 来显示进度对话框并启动任务
        SwingUtilities.invokeLater(() -> {
            progressDialog.setVisible(true);
            worker.execute();
        });
    }

    private File outputExcelFile(File srcFile) throws IOException {
        String targetSheetKeyword = "串口指令集";
        String matchedSheetName = null;

        // 使用 Apache POI 查找包含“指令集”的非隐藏工作表名称
        try (InputStream inputStream = Files.newInputStream(Paths.get(srcFile.getAbsolutePath()))) {
            Workbook workbook;
            if (srcFile.getName().endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                workbook = new XSSFWorkbook(inputStream);
            }
            int numberOfSheets = workbook.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++) {
                String sheetName = workbook.getSheetName(i);
                // 检查工作表是否包含关键字且未被隐藏
                if (sheetName.contains(targetSheetKeyword) && !workbook.isSheetHidden(i) && !workbook.isSheetVeryHidden(i)) {
                    matchedSheetName = sheetName;
                    break; // 找到第一个匹配的工作表后退出循环
                }
            }
        } catch (Exception e) {
            throw new IOException("读取Excel文件时出错: " + e.getMessage(), e);
        }

        if (matchedSheetName == null) {
            throw new IllegalArgumentException("未找到包含 \"" + targetSheetKeyword + "\" 的工作表");
        } else {
            log.info("找到包含{}的工作表:{}", targetSheetKeyword, matchedSheetName);
        }

        // 使用 EasyExcel 读取匹配的工作表数据
        EasyExcel.read(srcFile, ExcelData.class, excelListener)
                .sheet(matchedSheetName)
                .headRowNumber(2) // 根据您的需求调整标题行数
                .doRead();

        List<ExcelData> dataList = excelListener.getDataList();
        if (dataList.isEmpty()) {
            throw new IllegalArgumentException("原始Excel文件数据为空，请检查sheet名是否包含串口指令集，里面格式是否包含列名：指令帧描述和指令帧(举例)");
        }
        File parentFolder = srcFile.getParentFile();
        // 定义目标文件路径
        File targetFile = new File(parentFolder, String.format("ADS串口测试_%s.xlsm", DateUtils.getNowForFile()));
        // 检查目标文件是否已经存在，避免重复复制
        if (!targetFile.exists()) {
            // 从资源中复制模板文件到目标路径
            ResourceUtils.copyResourceToFile("templates/action_sequences_template.xlsm", targetFile);
        }
        writeData(dataList, targetFile.getAbsolutePath());
        return targetFile;
    }

    public File transferTestCase(String filePath) throws IOException {
        File targetFile = null;
        // 获取选定文件的路径
        File selectedFile = new File(filePath);
        if (selectedFile.exists() && selectedFile.isFile()) {
            // 对目标文件进行读写操作
            targetFile = outputExcelFile(selectedFile);
        }
        return targetFile;
    }

    public void writeData(List<ExcelData> dataList, String outputFileName) throws IOException {

        File outputFile = new File(outputFileName);
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new FileNotFoundException("输出文件为空或不存在: " + outputFileName);
        }
        try (InputStream inputStream = Files.newInputStream(Paths.get(outputFile.getAbsolutePath()))) {
            Workbook workbook;
            // 使用 try-with-resources 自动管理 Workbook
            if (outputFile.getName().endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                workbook = new XSSFWorkbook(inputStream);
            }
            Sheet sheet = workbook.getSheet("1DATM");
            if (sheet == null) {
                throw new IllegalArgumentException("工作簿中未找到名为 '1DATM' 的工作表.");
            }
            // 遍历数据列表并写入Excel
            for (int i = 0; i < dataList.size(); i++) {
                ExcelData excelData = dataList.get(i);
                Row row = sheet.getRow(2 + i / 2);
                if (i % 2 == 0) {
                    row.getCell(4).setCellValue(excelData.getDescription());
                    row.getCell(9).setCellValue(String.format("1.Serial-Send-%s-check-1", excelData.getCommand()));
                } else {
                    row.getCell(10).setCellValue(String.format("1.Serial-Receive-%s", excelData.getCommand()));
                }
            }
            // 保存更改到临时文件
            File tempFile = new File(outputFileName + ".tmp");
            try (FileOutputStream tempOutputStream = new FileOutputStream(tempFile)) {
                workbook.write(tempOutputStream);
                tempOutputStream.flush();
            }

            // 使用 Files.move 确保原子性
            Files.move(tempFile.toPath(), outputFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("成功写入数据到文件: {}", outputFileName);
        } catch (IOException e) {
            log.error("写入文件错误: {}", e.getMessage());
            throw e; // 重新抛出异常以便调用者处理
        }
    }
}