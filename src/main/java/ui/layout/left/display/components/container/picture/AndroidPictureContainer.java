package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.AndroidDevice;
import ui.base.picture.ADBPanel;
import ui.base.picture.AndroidPictureRectDrawLabel;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

@Slf4j
public class AndroidPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;

    // 视频流相关字段
    private InputStream videoInputStream;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private volatile boolean isStreamRunning = false;

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }


    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        tabbedPane.addTab("Android", panel);
//        tabbedPane.addTab("发送ADB命令与接收", new AdbSendPanel(this, getMainModel()));
        tabbedPane.addTab("ADB命令面板", new ADBPanel(getMainModel(), getDevice()));
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AndroidDevice androidDevice = (AndroidDevice) getDevice();
                OperationResult operationResult = androidDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AndroidPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AndroidPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AndroidDevice device = (AndroidDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning) {
            stopVideoStream();
        }

        log.info("开始启动Android设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接Android设备视频流: " + deviceName);

                    videoInputStream = device.videoStream();
                    if (videoInputStream == null) {
                        getPicturePanel().setText("无法启动视频流: 设备返回null");
                        return false;
                    }

                    isStreamRunning = true;
                    getPicturePanel().clearText();
                    completeGrab();

                    log.info("Android视频流启动成功，开始读取视频帧: {}", deviceName);

                    // 使用字节流方式读取视频帧
                    ByteArrayOutputStream imageBuffer = new ByteArrayOutputStream();
                    byte[] buffer = new byte[8096];
                    int bytesRead;

                    while (isStreamRunning && !isCancelled()) {
                        try {
                            while ((bytesRead = videoInputStream.read(buffer)) != -1) {
                                imageBuffer.write(buffer, 0, bytesRead);

                                // 检查是否收到完整的JPEG帧
                                byte[] data = imageBuffer.toByteArray();
                                if (isCompleteFrame(data)) {
                                    // 解析JPEG数据为BufferedImage
                                    BufferedImage frame = parseJpegFrame(data);
                                    if (frame != null) {
                                        publish(frame);
                                    }
                                    imageBuffer.reset();
                                }

                                // 防止缓冲区过大
                                if (imageBuffer.size() > 8 * 1024 * 1024) { // 8MB限制
                                    log.warn("视频流缓冲区过大，重置: {} bytes", imageBuffer.size());
                                    imageBuffer.reset();
                                }
                            }
                        } catch (Exception e) {
                            if (isStreamRunning) {
                                log.error("读取视频帧失败: {}, 错误: {}", deviceName, e.getMessage());
                                Thread.sleep(100); // 错误后短暂休眠
                            }
                        }
                    }

                    return true;
                } catch (Exception e) {
                    log.error("Android视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("Android视频流获取任务完成，但结果为失败");
                        getPicturePanel().setText("视频流启动失败，请检查设备连接状态");
                    }
                } catch (Exception e) {
                    log.error("Android视频流获取任务异常", e);
                    getPicturePanel().setText("视频流启动异常: " + e.getMessage());
                } finally {
                    stopVideoStream();
                }
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 停止视频流
     */
    private void stopVideoStream() {
        isStreamRunning = false;

        // 关闭视频流输入流
        if (videoInputStream != null) {
            try {
                videoInputStream.close();
                log.info("Android视频流输入流已关闭: {}", getDevice().getDeviceName());
            } catch (Exception e) {
                log.warn("关闭Android视频流输入流失败: {}, 错误: {}", getDevice().getDeviceName(), e.getMessage());
            } finally {
                videoInputStream = null;
            }
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("Android视频流已停止: {}", getDevice().getDeviceName());
    }

    @Override
    public void appExit() {
        stopVideoStream();
    }

    /**
     * 检查是否为完整的JPEG帧
     *
     * @param data 字节数据
     * @return 是否为完整帧
     */
    private boolean isCompleteFrame(byte[] data) {
        if (data.length < 4) {
            return false;
        }

        // 查找JPEG开始标识 (0xFF 0xD8)
        int startIndex = -1;
        for (int i = 0; i <= data.length - 2; i++) {
            if ((data[i] & 0xFF) == 0xFF && (data[i + 1] & 0xFF) == 0xD8) {
                startIndex = i;
                break;
            }
        }

        if (startIndex == -1) {
            return false; // 没有找到JPEG开始标识
        }

        // 查找JPEG结束标识 (0xFF 0xD9)
        for (int i = startIndex + 2; i <= data.length - 2; i++) {
            if ((data[i] & 0xFF) == 0xFF && (data[i + 1] & 0xFF) == 0xD9) {
                return true; // 找到完整的JPEG帧
            }
        }

        return false; // 没有找到JPEG结束标识
    }

    /**
     * 解析JPEG数据为BufferedImage
     *
     * @param jpegData JPEG字节数据
     * @return BufferedImage或null
     */
    private BufferedImage parseJpegFrame(byte[] jpegData) {
        try (ByteArrayInputStream imageStream = new ByteArrayInputStream(jpegData)) {
            return ImageIO.read(imageStream);
        } catch (IOException e) {
            log.debug("解析JPEG帧失败: {}", e.getMessage());
            return null;
        }
    }

}
