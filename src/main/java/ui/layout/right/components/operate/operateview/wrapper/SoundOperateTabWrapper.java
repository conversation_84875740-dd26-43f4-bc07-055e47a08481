package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.SoundOperatePanel;
import ui.model.MainModel;

public class SoundOperateTabWrapper extends DeviceOperateTabWrapper {
    public SoundOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_SOUND_CARD;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel("麦克风设备", new SoundOperatePanel(mainModel, DeviceModel.SoundCard.AUXIN_SOUND_DEVICE));
        addOperatePanel("研华USB4704设备", new SoundOperatePanel(mainModel, DeviceModel.SoundCard.USB4704_DEVICE));
    }

}
