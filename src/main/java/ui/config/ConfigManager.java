package ui.config;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;

@Slf4j
public class ConfigManager {
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".flytestApp";
    private static final String CONFIG_FILE = "settings.properties";
    private static ConfigManager instance;
    private final Properties properties;
    private final File configFile;

    private ConfigManager() {
        properties = new Properties();
        // 确保配置目录存在
        File dir = new File(CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        configFile = new File(dir, CONFIG_FILE);
        loadConfig();
    }

    public static ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }

    private void loadConfig() {
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                properties.load(fis);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public void saveConfig() {
        try (FileOutputStream fos = new FileOutputStream(configFile)) {
            properties.store(fos, "Application Settings");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    // 存储整数值
    public void setInt(String key, int value) {
        properties.setProperty(key, String.valueOf(value));
        saveConfig();
    }

    // 获取整数值，带默认值
    public int getInt(String key, int defaultValue) {
        String value = properties.getProperty(key);
        try {
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    // 存储字符串值
    public void setString(String key, String value) {
        properties.setProperty(key, value);
        saveConfig();
    }

    // 获取字符串值，带默认值
    public String getString(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }

    // 存储布尔值
    public void setBoolean(String key, boolean value) {
        properties.setProperty(key, String.valueOf(value));
        saveConfig();
    }

    // 获取布尔值，带默认值
    public boolean getBoolean(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        return value != null ? Boolean.parseBoolean(value) : defaultValue;
    }

    // 清除特定配置
    public void remove(String key) {
        properties.remove(key);
        saveConfig();
    }

    // 清除所有配置
    public void clear() {
        properties.clear();
        saveConfig();
    }
}